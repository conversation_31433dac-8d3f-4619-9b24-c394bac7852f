<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" :rules="formRules" label-width="80px" size="small">
        <el-form-item label="已选诊断" prop="choosedDiagnosis">
          <div class="tag-container">
            <el-tag
              v-for="(diagnosis, index) in choosedDiagnosisShow"
              :key="`tag-${index}-${diagnosis}`"
              closable
              @close="handleRemoveTag(index)"
              style="margin: 2px 5px 2px 0;">
              {{ diagnosis }}
            </el-tag>
            <div v-if="choosedDiagnosisShow.length === 0" class="el-form-item__error">
              请选择诊断
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入诊断" prop="diagnosis">
          <el-input
            placeholder="输入关键字过滤诊断"
            v-model="filterTextDiagnosis"
            size="small"
            @input="handleFilterInput"
            clearable>
            <i @click="initLeftDiagnosisTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree
            class="filter-tree-dept"
            :data="dataDiagnosis"
            show-checkbox
            node-key="id"
            :props="defaultProps"
            default-expand-all
            style="max-height: 300px;margin-top: 5px;"
            :filter-node-method="filterNodeDiagnosis"
            ref="treeDiagnosis"
            :expand-on-click-node="false"
            :highlight-current="true"
            check-strictly
            @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="handleCancel">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" :loading="loading">{{ $t('confirm')
        }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
import request from '@/plugin/axios'

export default {
  name: 'diagnosis-select',
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '选择诊断',
      filterTextDiagnosis: '',
      dataDiagnosis: [],
      defaultProps: {
        children: 'children',
        label: 'diseaseName'
      },
      dataForm: {
        choosedDiagnosis: []
      },
      localSelectedDiagnoses: [],
      selectedNodeIds: [], // 保存所有选中的节点 id（包括父节点）
      formRules: {
        choosedDiagnosis: [
          {
            validator: (rule, value, callback) => {
              if (this.choosedDiagnosisShow.length === 0) {
                callback(new Error('请选择诊断'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]
      }
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.resetForm()

      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',').filter(code => code.trim())
        const names = this.selectedName.split(',').filter(name => name.trim())

        this.localSelectedDiagnoses = codes.map((code, index) => ({
          id: code,
          diagnosisCode: code,
          diseaseName: names[index] || code
        }))
      } else {
        this.localSelectedDiagnoses = []
      }

      this.$nextTick(() => {
        this.initLeftDiagnosisTreeData()
      })
    },

    resetForm() {
      this.dataForm.choosedDiagnosis = []
      this.selectedNodeIds = []
      this.filterTextDiagnosis = ''
      this.dataDiagnosis = []
      if (this.$refs.dataForm) {
        this.$refs.dataForm.clearValidate()
      }
    },

    handleCancel() {
      this.visible = false
      this.resetForm()
    },

    handleFilterInput() {
      // 防抖处理
      clearTimeout(this.filterTimer)
      this.filterTimer = setTimeout(() => {
        if (this.$refs.treeDiagnosis) {
          this.$refs.treeDiagnosis.filter(this.filterTextDiagnosis)
        }
      }, 300)
    },

    initData(data) {
      this.$refs.treeDiagnosis.setCheckedKeys(data.split(','))
    },

    getParams() {
      return this.dataForm.choosedDiagnosis.map(item => item.pkid).join(',')
    },

    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, e => { })
    },

    handleCheckChange(checkedNode, isChecked) {
      if (isChecked) {
        this.handleCheckNode(checkedNode)
      } else {
        this.handleUncheckNode(checkedNode)
      }

      this.syncTreeCheckedStatus()
    },

    handleCheckNode(node) {
      const leafNodes = this.getAllLeafNodes(node)
      leafNodes.forEach(leaf => {
        const exists = this.dataForm.choosedDiagnosis.some(i => i.id === leaf.id)
        if (!exists) {
          this.dataForm.choosedDiagnosis.push(leaf)
        }
      })

      if (!this.selectedNodeIds.includes(node.id)) {
        this.selectedNodeIds.push(node.id)
      }
    },

    handleUncheckNode(node) {
      if (this.hasChildren(node)) {
        const leafNodes = this.getAllLeafNodes(node)
        leafNodes.forEach(leaf => {
          this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(i => i.id !== leaf.id)
        })

        this.selectedNodeIds = this.selectedNodeIds.filter(id => id !== node.id)
      } else {
        this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(i => i.id !== node.id)
      }
    },

    getAllLeafNodes(node) {
      const leaves = []
      const traverse = (n) => {
        if (!this.hasChildren(n)) {
          leaves.push(n)
        } else {
          n.children.forEach(traverse)
        }
      }
      traverse(node)
      return leaves
    },

    hasChildren(node) {
      return node.children && node.children.length > 0
    },

    syncTreeCheckedStatus() {
      const selectedIds = [
        ...this.localSelectedDiagnoses.map(i => i.id),
        ...this.dataForm.choosedDiagnosis.map(i => i.id),
        ...this.selectedNodeIds
      ]
      this.$refs.treeDiagnosis.setCheckedKeys(selectedIds)
    },

    initLeftDiagnosisTreeData() {
      this.loading = true
      const params = this.filterTextDiagnosis ? { diseaseName: this.filterTextDiagnosis } : {}

      request({
        url: 'business/busdiseasetypemanage/listTree',
        method: 'get',
        params
      }).then((res) => {
        this.dataDiagnosis = Array.isArray(res) ? res : []

        // 恢复之前选中的状态
        this.$nextTick(() => {
          this.restoreSelectedState()
          this.loading = false
        })
      }).catch((error) => {
        console.error('获取诊断树数据失败:', error)
        this.$message.error('获取诊断数据失败')
        this.loading = false
      })
    },

    restoreSelectedState() {
      if (!this.$refs.treeDiagnosis || !this.dataDiagnosis.length) return

      const selectedIds = this.localSelectedDiagnoses.map(s => s.id)

      const traverse = (nodes) => {
        for (const node of nodes) {
          if (selectedIds.includes(node.id)) {
            const leaves = this.getAllLeafNodes(node)
            leaves.forEach(leaf => {
              const exists = this.dataForm.choosedDiagnosis.some(i => i.id === leaf.id)
              if (!exists) {
                this.dataForm.choosedDiagnosis.push(leaf)
              }
            })

            if (!this.selectedNodeIds.includes(node.id)) {
              this.selectedNodeIds.push(node.id)
            }
          }

          if (selectedIds.includes(node.id) ||
            this.localSelectedDiagnoses.some(s => s.diagnosisCode === node.diagnosisCode)) {
            // 标记为选中
          }

          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        }
      }

      traverse(this.dataDiagnosis)
      this.syncTreeCheckedStatus()
    },

    filterNodeDiagnosis(value, data) {
      if (!value) return true
      return data.diseaseName.indexOf(value) !== -1
    },

    handleRemoveTag(index) {
      const totalSelected = this.localSelectedDiagnoses.length
      let removedDiagnosis = null

      if (index < totalSelected) {
        // 移除预选的诊断
        removedDiagnosis = this.localSelectedDiagnoses[index]
        this.localSelectedDiagnoses.splice(index, 1)
      } else {
        // 移除新选择的诊断
        const newIndex = index - totalSelected
        removedDiagnosis = this.dataForm.choosedDiagnosis[newIndex]
        this.dataForm.choosedDiagnosis.splice(newIndex, 1)
      }

      if (removedDiagnosis) {
        // 同步取消树中对应节点的选中状态
        if (this.$refs.treeDiagnosis) {
          this.$refs.treeDiagnosis.setChecked(removedDiagnosis.id, false)
        }

        // 从相关数组中移除
        this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(
          item => item.id !== removedDiagnosis.id
        )
        this.selectedNodeIds = this.selectedNodeIds.filter(id => id !== removedDiagnosis.id)

        // 强制同步树控件状态
        this.$nextTick(() => {
          this.syncTreeCheckedStatus()
          // 触发表单验证
          this.$refs.dataForm.validateField('choosedDiagnosis')
        })
      }
    },

    findNodeByDiagnosisCode(nodes, diagnosisCode) {
      for (const node of nodes) {
        if (node.diagnosisCode === diagnosisCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByDiagnosisCode(node.children, diagnosisCode)
          if (found) return found
        }
      }
      return null
    },

    findNodeById(nodes, nodeId) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, nodeId)
          if (found) return found
        }
      }
      return null
    },

    async dataFormSubmitHandle() {
      try {
        // 表单验证
        const valid = await this.$refs.dataForm.validate()
        if (!valid) {
          return
        }

        this.loading = true

        // 去重处理
        const allDiagnoses = [
          ...this.localSelectedDiagnoses,
          ...this.dataForm.choosedDiagnosis
        ]

        // 根据 id 去重
        const uniqueDiagnoses = allDiagnoses.filter((diagnosis, index, self) =>
          index === self.findIndex(d => d.id === diagnosis.id)
        )

        if (uniqueDiagnoses.length === 0) {
          this.$message.error('请选择诊断')
          return
        }

        const finalDiagnosisCode = uniqueDiagnoses.map(item => item.diagnosisCode).join(',')
        const finalDiseaseName = uniqueDiagnoses.map(item => item.diseaseName).join(',')

        this.$emit('callback', {
          diagnosisCode: finalDiagnosisCode,
          diseaseName: finalDiseaseName
        })

        this.visible = false
        this.resetForm()
      } catch (err) {
        console.error('提交失败:', err)
        this.$message.error('提交失败，请重试')
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    choosedDiagnosisShow() {
      const existingNames = this.localSelectedDiagnoses.map(v => v.diseaseName)
      const newNames = this.dataForm.choosedDiagnosis.map(v => v.diseaseName)
      return [...existingNames, ...newNames]
    },
    choosedDiagnosisCode() {
      return this.dataForm.choosedDiagnosis.length > 0
        ? (this.selectedCode ? this.selectedCode + ',' : '') + this.dataForm.choosedDiagnosis.map(v => v.diagnosisCode).join(',')
        : this.selectedCode
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.filterTimer) {
      clearTimeout(this.filterTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  max-height: 120px;
  margin-bottom: 5px;
  overflow-y: auto;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
}
</style>
