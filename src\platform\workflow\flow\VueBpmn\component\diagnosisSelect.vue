<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选诊断" prop="choosedDiagnosis">
          <div class="tag-container">
            <el-tag v-for="(diagnosis, index) in choosedDiagnosisShow" :key="index" closable
              @close="handleRemoveTag(index)" style="margin: 2px 5px 2px 0;">
              {{ diagnosis }}
            </el-tag>
            <div v-if="choosedDiagnosisShow.length === 0" class="el-form-item__error">
              请选择诊断
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入诊断" prop="diagnosis">
          <el-input placeholder="输入关键字过滤诊断" v-model="filterTextDiagnosis" size="small">
            <i @click="initLeftDiagnosisTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataDiagnosis" show-checkbox node-key="id" :props="defaultProps"
            default-expand-all style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeDiagnosis"
            ref="treeDiagnosis" :expand-on-click-node="false" :highlight-current="true" check-strictly
            @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
        }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
import request from '@/plugin/axios'

export default {
  name: 'diagnosis-select',
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '选择诊断',
      filterTextDiagnosis: '',
      dataDiagnosis: [],
      defaultProps: {
        children: 'children',
        label: 'diseaseName'
      },
      dataForm: {
        choosedDiagnosis: []
      },
      localSelectedDiagnoses: [],
      selectedNodeIds: [] // 新增：保存所有选中的节点 id（包括父节点）
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.dataForm.choosedDiagnosis = []

      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',')
        const names = this.selectedName.split(',')

        this.localSelectedDiagnoses = codes.map((code, index) => ({
          id: code,
          diagnosisCode: code,
          diseaseName: names[index]
        }))
      } else {
        this.localSelectedDiagnoses = []
      }

      this.$nextTick(() => {
        this.initLeftDiagnosisTreeData()
      })
    },

    initData(data) {
      this.$refs.treeDiagnosis.setCheckedKeys(data.split(','))
    },

    getParams() {
      return this.dataForm.choosedDiagnosis.map(item => item.pkid).join(',')
    },

    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, e => { })
    },

    handleCheckChange(checkedNode, isChecked) {
      if (isChecked) {
        this.handleCheckNode(checkedNode)
      } else {
        this.handleUncheckNode(checkedNode)
      }

      this.syncTreeCheckedStatus()
    },

    handleCheckNode(node) {
      const leafNodes = this.getAllLeafNodes(node)
      leafNodes.forEach(leaf => {
        const exists = this.dataForm.choosedDiagnosis.some(i => i.id === leaf.id)
        if (!exists) {
          this.dataForm.choosedDiagnosis.push(leaf)
        }
      })

      if (!this.selectedNodeIds.includes(node.id)) {
        this.selectedNodeIds.push(node.id)
      }
    },

    handleUncheckNode(node) {
      if (this.hasChildren(node)) {
        const leafNodes = this.getAllLeafNodes(node)
        leafNodes.forEach(leaf => {
          this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(i => i.id !== leaf.id)
        })

        this.selectedNodeIds = this.selectedNodeIds.filter(id => id !== node.id)
      } else {
        this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(i => i.id !== node.id)
      }
    },

    getAllLeafNodes(node) {
      const leaves = []
      const traverse = (n) => {
        if (!this.hasChildren(n)) {
          leaves.push(n)
        } else {
          n.children.forEach(traverse)
        }
      }
      traverse(node)
      return leaves
    },

    hasChildren(node) {
      return node.children && node.children.length > 0
    },

    syncTreeCheckedStatus() {
      const selectedIds = [
        ...this.localSelectedDiagnoses.map(i => i.id),
        ...this.dataForm.choosedDiagnosis.map(i => i.id),
        ...this.selectedNodeIds
      ]
      this.$refs.treeDiagnosis.setCheckedKeys(selectedIds)
    },

    initLeftDiagnosisTreeData() {
      if (this.filterTextDiagnosis) {
        request({
          url: 'business/busdiseasetypemanage/listTree',
          method: 'get',
          params: { diseaseName: this.filterTextDiagnosis }
        }).then((res) => {
          this.dataDiagnosis = res

          const selectedIds = this.localSelectedDiagnoses.map(s => s.id)
          const checkedIds = []

          const traverse = (nodes) => {
            for (const node of nodes) {
              if (selectedIds.includes(node.id)) {
                const leaves = this.getAllLeafNodes(node)
                leaves.forEach(leaf => {
                  const exists = this.dataForm.choosedDiagnosis.some(i => i.id === leaf.id)
                  if (!exists) {
                    this.dataForm.choosedDiagnosis.push(leaf)
                  }
                })

                if (!this.selectedNodeIds.includes(node.id)) {
                  this.selectedNodeIds.push(node.id)
                }
              }

              if (selectedIds.includes(node.id) ||
                this.localSelectedDiagnoses.some(s => s.diagnosisCode === node.diagnosisCode)) {
                checkedIds.push(node.id)
              }

              if (node.children && node.children.length > 0) {
                traverse(node.children)
              }
            }
          }

          traverse(this.dataDiagnosis)

          this.syncTreeCheckedStatus()
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }
    },

    filterNodeDiagnosis(value, data) {
      if (!value) return true
      return data.diseaseName.indexOf(value) !== -1
    },

    handleRemoveTag(index) {
      const totalSelected = this.localSelectedDiagnoses.length
      if (index < totalSelected) {
        const removedDiagnosis = this.localSelectedDiagnoses[index]
        this.localSelectedDiagnoses.splice(index, 1)

        // 同步取消树中对应节点的选中状态
        if (this.$refs.treeDiagnosis) {
          const node = this.findNodeById(this.dataDiagnosis, removedDiagnosis.id)
          if (node) {
            this.$refs.treeDiagnosis.setChecked(node.id, false)
          }
        }

        // 从 choosedDiagnosis 中移除（如果存在）
        this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(
          item => item.id !== removedDiagnosis.id
        )

        this.selectedNodeIds = this.selectedNodeIds.filter(id => id !== removedDiagnosis.id)

      } else {
        const newIndex = index - totalSelected
        const removedDiagnosis = this.dataForm.choosedDiagnosis[newIndex]
        this.dataForm.choosedDiagnosis.splice(newIndex, 1)

        if (this.$refs.treeDiagnosis && removedDiagnosis) {
          this.$refs.treeDiagnosis.setChecked(removedDiagnosis.id, false)
        }
      }

      this.syncTreeCheckedStatus() // ✅ 关键：强制同步树控件状态
    },

    findNodeByDiagnosisCode(nodes, diagnosisCode) {
      for (const node of nodes) {
        if (node.diagnosisCode === diagnosisCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByDiagnosisCode(node.children, diagnosisCode)
          if (found) return found
        }
      }
      return null
    },

    findNodeById(nodes, nodeId) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, nodeId)
          if (found) return found
        }
      }
      return null
    },

    async dataFormSubmitHandle() {
      if (this.choosedDiagnosisShow.length === 0) {
        this.$message.error('请选择诊断')
        return
      }
      this.loading = true
      try {
        const allDiagnoses = [
          ...this.localSelectedDiagnoses,
          ...this.dataForm.choosedDiagnosis
        ]
        const finalDiagnosisCode = allDiagnoses.map(item => item.diagnosisCode).join(',')
        const finalDiseaseName = allDiagnoses.map(item => item.diseaseName).join(',')

        this.$emit('callback', {
          diagnosisCode: finalDiagnosisCode,
          diseaseName: finalDiseaseName
        })
        this.visible = false
      } catch (err) {
        console.error(err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    choosedDiagnosisShow() {
      const existingNames = this.localSelectedDiagnoses.map(v => v.diseaseName)
      const newNames = this.dataForm.choosedDiagnosis.map(v => v.diseaseName)
      return [...existingNames, ...newNames]
    },
    choosedDiagnosisCode() {
      return this.dataForm.choosedDiagnosis.length > 0
        ? (this.selectedCode ? this.selectedCode + ',' : '') + this.dataForm.choosedDiagnosis.map(v => v.diagnosisCode).join(',')
        : this.selectedCode
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  max-height: 120px;
  margin-bottom: 5px;
  overflow-y: auto;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
}
</style>