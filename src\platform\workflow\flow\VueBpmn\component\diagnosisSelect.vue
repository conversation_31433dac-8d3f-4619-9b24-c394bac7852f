<template>
  <qz-dialog :title="title" :visible.sync="visible" top="6vh" width="600px">
    <div class="KS-Form">
      <el-form ref="dataForm" :model="dataForm" label-width="80px" size="small">
        <el-form-item label="已选诊断" prop="choosedDiagnosis">
          <div class="tag-container">
            <el-tag v-for="(diagnosis, index) in choosedDiagnosisShow" :key="index" closable
              @close="handleRemoveTag(index)" style="margin: 2px 5px 2px 0;">
              {{ diagnosis }}
            </el-tag>
            <div v-if="choosedDiagnosisShow.length === 0" class="el-form-item__error">
              请选择诊断
            </div>
          </div>
        </el-form-item>
        <el-form-item label="输入诊断" prop="diagnosis">
          <el-input placeholder="输入关键字过滤诊断" v-model="filterTextDiagnosis" size="small">
            <i @click="initLeftDiagnosisTreeData" class="el-input__icon el-icon-search" slot="suffix"
              style="cursor: pointer"></i>
          </el-input>
          <el-tree class="filter-tree-dept" :data="dataDiagnosis" show-checkbox node-key="id" :props="defaultProps"
            default-expand-all style="max-height: 300px;margin-top: 5px;" :filter-node-method="filterNodeDiagnosis"
            ref="treeDiagnosis" :expand-on-click-node="false" :highlight-current="true" check-strictly
            @check-change="handleCheckChange">
          </el-tree>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button size="small" @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button size="small" @click="dataFormSubmitHandle()" type="primary" v-loading="loading">{{ $t('confirm')
      }}</el-button>
    </template>
  </qz-dialog>
</template>

<script>
import request from '@/plugin/axios'

export default {
  name: 'diagnosis-select',
  props: {
    selectedName: {
      type: String,
      default: ''
    },
    selectedCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '选择诊断',
      filterTextDiagnosis: '',
      dataDiagnosis: [],
      defaultProps: {
        children: 'children',
        label: 'diseaseName'
      },
      dataForm: {
        choosedDiagnosis: []
      },
      localSelectedDiagnoses: [],
      selectedNodeIds: [] // 新增：保存所有选中的节点 id（包括父节点）
    }
  },
  methods: {
    init(data) {
      this.visible = true
      this.resetFormData()

      if (this.selectedCode && this.selectedName) {
        const codes = this.selectedCode.split(',').filter(code => code.trim())
        const names = this.selectedName.split(',').filter(name => name.trim())

        this.localSelectedDiagnoses = codes.map((code, index) => ({
          id: code,
          diagnosisCode: code,
          diseaseName: names[index] || code
        }))

        console.log('Initialized with selected diagnoses:', this.localSelectedDiagnoses)
      } else {
        this.localSelectedDiagnoses = []
      }
    },

    resetFormData() {
      this.dataForm.choosedDiagnosis = []
      this.selectedNodeIds = []
      this.filterTextDiagnosis = ''
    },

    initData(data) {
      this.$refs.treeDiagnosis.setCheckedKeys(data.split(','))
    },

    getParams() {
      return this.dataForm.choosedDiagnosis.map(item => item.pkid).join(',')
    },

    validateForm() {
      return this.$refs.dataForm.validate().then(v => v, () => false)
    },

    handleCheckChange(checkedNode, isChecked) {
      if (isChecked) {
        this.handleCheckNode(checkedNode)
      } else {
        this.handleUncheckNode(checkedNode)
      }
    },

    handleCheckNode(node) {
      if (this.hasChildren(node)) {
        // 如果是父节点，添加所有子叶节点
        const leafNodes = this.getAllLeafNodes(node)
        leafNodes.forEach(leaf => {
          const exists = this.dataForm.choosedDiagnosis.some(i => i.id === leaf.id)
          if (!exists) {
            this.dataForm.choosedDiagnosis.push(leaf)
          }
        })
        // 记录父节点ID
        if (!this.selectedNodeIds.includes(node.id)) {
          this.selectedNodeIds.push(node.id)
        }
      } else {
        // 如果是叶节点，直接添加
        const exists = this.dataForm.choosedDiagnosis.some(i => i.id === node.id)
        if (!exists) {
          this.dataForm.choosedDiagnosis.push(node)
        }
        // 记录叶节点ID
        if (!this.selectedNodeIds.includes(node.id)) {
          this.selectedNodeIds.push(node.id)
        }
      }
    },

    handleUncheckNode(node) {
      if (this.hasChildren(node)) {
        // 如果是父节点，移除所有子叶节点
        const leafNodes = this.getAllLeafNodes(node)
        leafNodes.forEach(leaf => {
          this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(i => i.id !== leaf.id)
        })
        // 移除父节点ID
        this.selectedNodeIds = this.selectedNodeIds.filter(id => id !== node.id)
      } else {
        // 如果是叶节点，直接移除
        this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(i => i.id !== node.id)
        // 同时从selectedNodeIds中移除
        this.selectedNodeIds = this.selectedNodeIds.filter(id => id !== node.id)
      }
    },

    getAllLeafNodes(node) {
      const leaves = []
      const traverse = (n) => {
        if (!this.hasChildren(n)) {
          leaves.push(n)
        } else {
          n.children.forEach(traverse)
        }
      }
      traverse(node)
      return leaves
    },

    hasChildren(node) {
      return node.children && node.children.length > 0
    },

    syncTreeCheckedStatus() {
      if (!this.$refs.treeDiagnosis) return

      // 收集所有应该选中的节点ID
      const allSelectedIds = []

      // 添加预选的诊断ID
      this.localSelectedDiagnoses.forEach(diagnosis => {
        allSelectedIds.push(diagnosis.id)
      })

      // 添加新选择的诊断ID
      this.dataForm.choosedDiagnosis.forEach(diagnosis => {
        allSelectedIds.push(diagnosis.id)
      })

      // 添加父节点ID
      this.selectedNodeIds.forEach(id => {
        allSelectedIds.push(id)
      })

      // 去重并设置选中状态
      const uniqueIds = [...new Set(allSelectedIds)]
      console.log('Setting checked keys:', uniqueIds)
      this.$refs.treeDiagnosis.setCheckedKeys(uniqueIds)
    },

    initLeftDiagnosisTreeData() {
      this.loading = true
      const params = this.filterTextDiagnosis ? { diseaseName: this.filterTextDiagnosis } : {}

      request({
        url: 'business/busdiseasetypemanage/listTree',
        method: 'get',
        params
      }).then((res) => {
        this.dataDiagnosis = Array.isArray(res) ? res : []

        // 在数据加载完成后，恢复选中状态
        this.$nextTick(() => {
          this.restoreTreeCheckedStatus()
          this.loading = false
        })
      }).catch((error) => {
        console.error('获取诊断树数据失败:', error)
        this.$message.error('获取诊断数据失败')
        this.loading = false
      })
    },

    // 新增方法：恢复树的选中状态
    restoreTreeCheckedStatus() {
      if (!this.$refs.treeDiagnosis || !this.dataDiagnosis.length) return

      // 收集所有需要选中的节点ID
      const nodesToCheck = []

      // 遍历预选的诊断，找到对应的树节点
      this.localSelectedDiagnoses.forEach(diagnosis => {
        const node = this.findNodeInTree(this.dataDiagnosis, diagnosis.id, diagnosis.diagnosisCode)
        if (node) {
          nodesToCheck.push(node.id)
          // 如果找到的是父节点，也要记录到selectedNodeIds中
          if (this.hasChildren(node) && !this.selectedNodeIds.includes(node.id)) {
            this.selectedNodeIds.push(node.id)
          }
        }
      })

      // 添加已选择的诊断节点ID
      this.dataForm.choosedDiagnosis.forEach(diagnosis => {
        nodesToCheck.push(diagnosis.id)
      })

      // 添加父节点ID
      this.selectedNodeIds.forEach(id => {
        nodesToCheck.push(id)
      })

      // 去重并设置选中状态
      const uniqueIds = [...new Set(nodesToCheck)]
      console.log('Restoring checked keys:', uniqueIds)
      this.$refs.treeDiagnosis.setCheckedKeys(uniqueIds)
    },

    // 新增方法：在树中查找节点
    findNodeInTree(nodes, nodeId, diagnosisCode) {
      for (const node of nodes) {
        // 优先按ID匹配，其次按diagnosisCode匹配
        if (node.id === nodeId || node.diagnosisCode === diagnosisCode) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeInTree(node.children, nodeId, diagnosisCode)
          if (found) return found
        }
      }
      return null
    },

    filterNodeDiagnosis(value, data) {
      if (!value) return true
      return data.diseaseName.indexOf(value) !== -1
    },

    handleRemoveTag(index) {
      const totalSelected = this.localSelectedDiagnoses.length
      let removedDiagnosis = null

      if (index < totalSelected) {
        // 移除预选的诊断
        removedDiagnosis = this.localSelectedDiagnoses[index]
        this.localSelectedDiagnoses.splice(index, 1)
      } else {
        // 移除新选择的诊断
        const newIndex = index - totalSelected
        removedDiagnosis = this.dataForm.choosedDiagnosis[newIndex]
        this.dataForm.choosedDiagnosis.splice(newIndex, 1)
      }

      if (removedDiagnosis) {
        // 从相关数组中移除
        this.dataForm.choosedDiagnosis = this.dataForm.choosedDiagnosis.filter(
          item => item.id !== removedDiagnosis.id
        )
        this.selectedNodeIds = this.selectedNodeIds.filter(id => id !== removedDiagnosis.id)

        // 同步取消树中对应节点的选中状态
        if (this.$refs.treeDiagnosis) {
          this.$refs.treeDiagnosis.setChecked(removedDiagnosis.id, false)
        }
      }
    },

    findNodeByDiagnosisCode(nodes, diagnosisCode) {
      for (const node of nodes) {
        if (node.diagnosisCode === diagnosisCode) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByDiagnosisCode(node.children, diagnosisCode)
          if (found) return found
        }
      }
      return null
    },

    findNodeById(nodes, nodeId) {
      for (const node of nodes) {
        if (node.id === nodeId) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, nodeId)
          if (found) return found
        }
      }
      return null
    },

    async dataFormSubmitHandle() {
      if (this.choosedDiagnosisShow.length === 0) {
        this.$message.error('请选择诊断')
        return
      }
      this.loading = true
      try {
        const allDiagnoses = [
          ...this.localSelectedDiagnoses,
          ...this.dataForm.choosedDiagnosis
        ]
        const finalDiagnosisCode = allDiagnoses.map(item => item.diagnosisCode).join(',')
        const finalDiseaseName = allDiagnoses.map(item => item.diseaseName).join(',')

        this.$emit('callback', {
          diagnosisCode: finalDiagnosisCode,
          diseaseName: finalDiseaseName
        })
        this.visible = false
      } catch (err) {
        console.error(err)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    choosedDiagnosisShow() {
      const existingNames = this.localSelectedDiagnoses.map(v => v.diseaseName)
      const newNames = this.dataForm.choosedDiagnosis.map(v => v.diseaseName)
      // 去重处理，避免重复显示
      const allNames = [...existingNames, ...newNames]
      return [...new Set(allNames)]
    },
    choosedDiagnosisCode() {
      return this.dataForm.choosedDiagnosis.length > 0
        ? (this.selectedCode ? this.selectedCode + ',' : '') + this.dataForm.choosedDiagnosis.map(v => v.diagnosisCode).join(',')
        : this.selectedCode
    }
  },
  watch: {
    // 监听选中诊断的变化，用于调试
    'dataForm.choosedDiagnosis': {
      handler(newVal) {
        console.log('choosedDiagnosis changed:', newVal.map(v => v.diseaseName))
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  min-height: 32px;
  max-height: 120px;
  margin-bottom: 5px;
  overflow-y: auto;
}

.filter-tree-dept {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px;
  overflow-y: auto;
}
</style>